"""
策略验证器实现
"""
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from ..core.interfaces.strategy_validator import (
    IStrategyValidator, ValidationConfig, ValidationResult, ValidationSummary
)
from ..core.interfaces.data_access import IDataAccess
from ..strategies.strategy_manager import StrategyManager


class StrategyValidator(IStrategyValidator):
    """策略验证器实现"""

    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
        self.strategy_manager = StrategyManager(data_access)
        self.logger = logging.getLogger(__name__)

    def validate_strategy(self, config: ValidationConfig) -> ValidationSummary:
        """验证策略"""
        self.logger.info(f"开始验证策略: {config.strategy_name}")
        self.logger.info(f"测试日期数量: {len(config.test_dates)}")

        start_time = time.time()
        results = []
        successful_tests = 0
        failed_tests = 0

        for test_date in config.test_dates:
            try:
                self.logger.info(f"验证日期: {test_date.date()}")

                result = self.validate_single_date(
                    strategy_name=config.strategy_name,
                    test_date=test_date,
                    max_stocks=config.max_stocks_per_test,
                    stock_pool=config.stock_pool
                )

                results.append(result)

                if result.error_message is None:
                    successful_tests += 1
                    self.logger.info(f"验证成功，选中 {result.selection_count} 只股票")
                else:
                    failed_tests += 1
                    self.logger.warning(f"验证失败: {result.error_message}")

                # 保存结果
                if config.save_results:
                    self.save_validation_result(result)

            except Exception as e:
                failed_tests += 1
                error_result = ValidationResult(
                    test_date=test_date,
                    strategy_name=config.strategy_name,
                    selected_stocks=[],
                    total_candidates=0,
                    selection_count=0,
                    selection_rate=0.0,
                    strategy_config={},
                    execution_time=0.0,
                    error_message=str(e)
                )
                results.append(error_result)
                self.logger.error(f"验证日期 {test_date.date()} 失败: {str(e)}")

        # 计算汇总统计
        total_execution_time = time.time() - start_time
        valid_results = [r for r in results if r.error_message is None]

        avg_selection_count = 0.0
        avg_selection_rate = 0.0
        if valid_results:
            avg_selection_count = sum(r.selection_count for r in valid_results) / len(valid_results)
            avg_selection_rate = sum(r.selection_rate for r in valid_results) / len(valid_results)

        summary = ValidationSummary(
            config=config,
            results=results,
            total_tests=len(config.test_dates),
            successful_tests=successful_tests,
            failed_tests=failed_tests,
            avg_selection_count=avg_selection_count,
            avg_selection_rate=avg_selection_rate,
            total_execution_time=total_execution_time
        )

        self.logger.info(f"验证完成，成功 {successful_tests} 次，失败 {failed_tests} 次")
        return summary

    def validate_single_date(self, strategy_name: str,
                           test_date: datetime,
                           max_stocks: int = 20,
                           stock_pool: Optional[List[str]] = None) -> ValidationResult:
        """验证单个日期"""
        start_time = time.time()

        try:
            # 创建策略实例
            strategy = self.strategy_manager.create_strategy(strategy_name)
            if not strategy:
                raise ValueError(f"无法创建策略: {strategy_name}")

            # 获取股票池
            if stock_pool:
                candidate_stocks = stock_pool
            else:
                candidate_stocks = self.data_access.get_all_stock_codes()
                # 限制候选股票数量以提高验证速度，使用固定的种子确保结果一致性
                import random
                # 使用测试日期作为随机种子，确保同一天的验证结果一致
                random.seed(test_date.toordinal())
                candidate_stocks = random.sample(candidate_stocks, min(500, len(candidate_stocks)))

            selected_stocks = []

            # 计算需要的历史数据范围
            start_date = test_date - timedelta(days=60)  # 获取足够的历史数据

            for stock_code in candidate_stocks:
                try:
                    # 获取股票基本信息
                    stock_info = self.data_access.get_stock_info(stock_code)
                    if not stock_info:
                        continue

                    # 获取到测试日期为止的交易数据
                    trading_data = self.data_access.get_stock_data(stock_code, start_date, test_date)
                    if len(trading_data) < 30:  # 至少需要30天数据
                        continue

                    # 使用策略的内部分析方法，传递测试日期确保结果一致性
                    if hasattr(strategy, '_analyze_technical_reversal'):
                        result = strategy._analyze_technical_reversal(stock_code, stock_info, trading_data, test_date)
                        if result:
                            # 提取关键指标
                            stock_result = self._extract_key_indicators(result, trading_data)
                            stock_result['test_date'] = test_date.date()
                            selected_stocks.append(stock_result)

                except Exception as e:
                    self.logger.debug(f"处理股票 {stock_code} 时出错: {str(e)}")
                    continue

            # 按评分排序并限制结果数量
            selected_stocks.sort(key=lambda x: x.get('score', 0), reverse=True)
            selected_stocks = selected_stocks[:max_stocks]

            execution_time = time.time() - start_time
            selection_rate = len(selected_stocks) / len(candidate_stocks) * 100 if candidate_stocks else 0

            return ValidationResult(
                test_date=test_date,
                strategy_name=strategy_name,
                selected_stocks=selected_stocks,
                total_candidates=len(candidate_stocks),
                selection_count=len(selected_stocks),
                selection_rate=selection_rate,
                strategy_config=strategy.get_config(),
                execution_time=execution_time,
                error_message=None
            )

        except Exception as e:
            execution_time = time.time() - start_time
            return ValidationResult(
                test_date=test_date,
                strategy_name=strategy_name,
                selected_stocks=[],
                total_candidates=0,
                selection_count=0,
                selection_rate=0.0,
                strategy_config={},
                execution_time=execution_time,
                error_message=str(e)
            )

    def _extract_key_indicators(self, strategy_result: Dict, trading_data: List[Dict]) -> Dict:
        """提取关键指标"""
        # 获取最新交易数据
        latest_data = trading_data[-1] if trading_data else {}

        # 基本信息
        result = {
            'stock_code': strategy_result.get('stock_code', ''),
            'stock_name': strategy_result.get('stock_name', ''),
            'score': strategy_result.get('score', 0),
            'reason': strategy_result.get('reason', ''),
            'close_price': strategy_result.get('close_price', 0)
        }

        # 提取RSI和量比等关键指标
        if hasattr(strategy_result, 'get'):
            # 从策略结果中提取指标
            result['rsi'] = strategy_result.get('rsi', 0)
            result['volume_ratio'] = strategy_result.get('volume_ratio', 0)
            result['price_position'] = strategy_result.get('price_position', 0)
            result['bollinger_position'] = strategy_result.get('bollinger_position', 0)

        # 如果策略结果中没有指标，尝试从最新交易数据中获取
        if latest_data and 'rsi' not in result:
            # 这里可以根据实际的数据结构调整
            for key in ['rsi', 'volume_ratio', 'price_position', 'bollinger_position']:
                if key in latest_data:
                    result[key] = latest_data[key]

        return result

    def save_validation_result(self, result: ValidationResult) -> bool:
        """保存验证结果"""
        try:
            # TODO: 实现保存验证结果到数据库
            self.logger.info(f"保存验证结果: {result.test_date.date()}, 选中 {result.selection_count} 只股票")
            return True
        except Exception as e:
            self.logger.error(f"保存验证结果失败: {str(e)}")
            return False

    def get_validation_history(self, strategy_name: str = None,
                             start_date: datetime = None,
                             end_date: datetime = None) -> List[ValidationResult]:
        """获取验证历史"""
        try:
            # TODO: 实现从数据库获取验证历史
            self.logger.info("获取验证历史功能待实现")
            return []
        except Exception as e:
            self.logger.error(f"获取验证历史失败: {str(e)}")
            return []

    def generate_validation_report(self, summary: ValidationSummary) -> str:
        """生成验证报告"""
        report = []
        report.append("=" * 80)
        report.append(f"策略验证报告: {summary.config.strategy_name}")
        report.append("=" * 80)

        # 基本信息
        report.append(f"\n基本信息:")
        report.append(f"策略名称: {summary.config.strategy_name}")
        report.append(f"测试日期数量: {summary.total_tests}")
        report.append(f"成功测试: {summary.successful_tests}")
        report.append(f"失败测试: {summary.failed_tests}")
        report.append(f"总执行时间: {summary.total_execution_time:.2f} 秒")

        # 统计信息
        report.append(f"\n统计信息:")
        report.append(f"平均选股数量: {summary.avg_selection_count:.1f}")
        report.append(f"平均选股比例: {summary.avg_selection_rate:.2f}%")

        # 详细结果
        report.append(f"\n详细验证结果:")
        report.append("-" * 80)
        report.append(f"{'测试日期':<12} {'选股数量':<8} {'选股比例':<10} {'执行时间':<10} {'状态':<10}")
        report.append("-" * 80)

        for result in summary.results:
            status = "成功" if result.error_message is None else "失败"
            report.append(
                f"{result.test_date.date():<12} "
                f"{result.selection_count:<8} "
                f"{result.selection_rate:<9.2f}% "
                f"{result.execution_time:<9.2f}s "
                f"{status:<10}"
            )

            # 如果有错误，显示错误信息
            if result.error_message:
                report.append(f"    错误: {result.error_message}")

        # 选股详情（显示最近几次的详细结果）
        successful_results = [r for r in summary.results if r.error_message is None and r.selected_stocks]
        if successful_results:
            report.append(f"\n最近选股详情 (最多显示3次):")
            report.append("=" * 80)

            for result in successful_results[-3:]:  # 显示最近3次
                report.append(f"\n{result.test_date.date()} 选股结果:")
                report.append("-" * 60)

                if result.selected_stocks:
                    report.append(f"{'股票代码':<10} {'股票名称':<12} {'评分':<8} {'RSI':<8} {'量比':<8} {'价格位置':<10}")
                    report.append("-" * 60)

                    for stock in result.selected_stocks[:10]:  # 每次最多显示10只
                        report.append(
                            f"{stock.get('stock_code', ''):<10} "
                            f"{stock.get('stock_name', ''):<12} "
                            f"{stock.get('score', 0):<7.1f} "
                            f"{stock.get('rsi', 0):<7.1f} "
                            f"{stock.get('volume_ratio', 0):<7.2f} "
                            f"{stock.get('price_position', 0):<9.2f}"
                        )

                    if len(result.selected_stocks) > 10:
                        report.append(f"... 还有 {len(result.selected_stocks) - 10} 只股票")
                else:
                    report.append("未选中任何股票")

        # 验证评估
        report.append(f"\n验证评估:")
        report.append("-" * 50)

        success_rate = summary.successful_tests / summary.total_tests * 100 if summary.total_tests > 0 else 0

        if success_rate >= 90:
            report.append("✓ 策略运行稳定，成功率超过90%")
        elif success_rate >= 70:
            report.append("✓ 策略运行良好，成功率超过70%")
        elif success_rate >= 50:
            report.append("△ 策略运行一般，成功率在50-70%之间")
        else:
            report.append("✗ 策略运行不稳定，成功率低于50%")

        if summary.avg_selection_count > 10:
            report.append("✓ 策略选股数量充足，平均超过10只")
        elif summary.avg_selection_count > 5:
            report.append("△ 策略选股数量适中，平均5-10只")
        elif summary.avg_selection_count > 0:
            report.append("△ 策略选股数量较少，平均少于5只")
        else:
            report.append("✗ 策略基本选不出股票，可能条件过于严格")

        if summary.avg_selection_rate > 5:
            report.append("△ 选股比例较高，可能条件过于宽松")
        elif summary.avg_selection_rate > 1:
            report.append("✓ 选股比例适中，在1-5%之间")
        elif summary.avg_selection_rate > 0.1:
            report.append("✓ 选股比例较低，筛选较为严格")
        else:
            report.append("✗ 选股比例过低，可能条件过于严格")

        return "\n".join(report)
